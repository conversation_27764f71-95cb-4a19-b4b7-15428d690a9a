import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs';
import { google } from '@ai-sdk/google';
import { generateText } from 'ai';
import { z } from 'zod';
import { getUserByClerkId } from '@/lib/actions/user.action';
import { saveEventTasks, checkTasksExist } from '@/lib/actions/task.action';

// Zod schema for request body
const requestSchema = z.object({
	eventType: z.string(),
	eventTitle: z.string().optional(),
	eventDescription: z.string().optional(),
	isSubEvent: z.boolean().optional(),
	eventDetails: z
		.object({
			location: z.string().optional(),
			isOnline: z.boolean().optional(),
			capacity: z.number().optional(),
			startDate: z.string().optional(),
			endDate: z.string().optional(),
			isFree: z.boolean().optional(),
			price: z.number().optional(),
			category: z.string().optional(),
		})
		.optional(),
	eventId: z.string(),
	forceRegenerate: z.boolean().optional().default(false),
});

export async function POST(request: NextRequest) {
	// Declare variables outside try block to make them accessible in catch
	let mongoUser: any = null;
	let eventId: string = '';
	let isSubEvent: boolean = false;

	try {
		const { userId } = await auth();
		if (!userId) {
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		// Get MongoDB user ID
		mongoUser = await getUserByClerkId(userId);
		if (!mongoUser) {
			return NextResponse.json({ error: 'User not found' }, { status: 404 });
		}

		const body = await request.json();
		const {
			eventType,
			eventTitle,
			eventDescription,
			isSubEvent: parsedIsSubEvent,
			eventDetails,
			eventId: parsedEventId,
			forceRegenerate,
		} = requestSchema.parse(body);

		// Assign to variables accessible in catch block
		isSubEvent = parsedIsSubEvent || false;
		eventId = parsedEventId;

		// Check if tasks already exist for this event
		if (!forceRegenerate) {
			const existingTasksResult = await checkTasksExist(eventId, userId);
			if (existingTasksResult.success && existingTasksResult.exists) {
				return NextResponse.json({
					success: true,
					message: 'Tasks already exist for this event',
					tasksExist: true,
				});
			}
		}

		// Build context for AI generation
		const eventContext = eventDetails
			? `
      Event Details:
      - Location: ${
				eventDetails.isOnline ? 'Online Event' : eventDetails.location || 'TBD'
			}
      - Capacity: ${eventDetails.capacity || 'Unlimited'}
      - Duration: ${
				eventDetails.startDate && eventDetails.endDate
					? `${new Date(
							eventDetails.startDate
					  ).toLocaleDateString()} to ${new Date(
							eventDetails.endDate
					  ).toLocaleDateString()}`
					: 'TBD'
			}
      - Pricing: ${
				eventDetails.isFree
					? 'Free Event'
					: `₹${eventDetails.price || 0} per ticket`
			}
      - Category: ${eventDetails.category || 'General'}
    `
			: '';

		// Build event-specific context based on event type
		let eventSpecificTasks = '';
		const eventTypeLower = eventType.toLowerCase();

		if (
			eventTypeLower.includes('hackathon') ||
			eventTypeLower.includes('competition')
		) {
			eventSpecificTasks = `
Focus on technical infrastructure and competition management:
- Internet connectivity and backup solutions
- Power outlets and extension cords for participants
- Technical support team setup
- Judging criteria and evaluation systems
- Mentorship coordination
- Security and access control
- Workspace organization
- Presentation equipment setup`;
		} else if (
			eventTypeLower.includes('seminar') ||
			eventTypeLower.includes('workshop') ||
			eventTypeLower.includes('conference')
		) {
			eventSpecificTasks = `
Focus on learning environment and speaker support:
- Water bottles and refreshments for speakers
- Audio/visual equipment testing
- Seating arrangement optimization
- Speaker liaison and green room setup
- Material distribution planning
- Note-taking facilities
- Break time coordination
- Networking space preparation`;
		} else if (
			eventTypeLower.includes('cultural') ||
			eventTypeLower.includes('festival') ||
			eventTypeLower.includes('celebration')
		) {
			eventSpecificTasks = `
Focus on entertainment and cultural elements:
- Stage and performance area setup
- Decoration and ambiance creation
- Traditional elements coordination
- Entertainment schedule management
- Cultural sensitivity considerations
- Photography and videography setup
- Crowd management and safety
- Food and beverage coordination`;
		} else if (
			eventTypeLower.includes('sports') ||
			eventTypeLower.includes('athletic') ||
			eventTypeLower.includes('tournament')
		) {
			eventSpecificTasks = `
Focus on athletic facilities and safety:
- Equipment and gear preparation
- Safety measures and first aid
- Scoring and timing systems
- Referee and official coordination
- Participant registration and verification
- Venue preparation and marking
- Awards and recognition ceremony
- Weather contingency planning`;
		} else {
			eventSpecificTasks = `
Focus on general event management:
- Venue setup and logistics
- Registration and check-in process
- Technology and equipment needs
- Catering and refreshment planning
- Safety and emergency procedures`;
		}

		// Reduce task counts significantly
		const taskCount = isSubEvent ? '3-4' : '4-5';
		const subtaskCount = isSubEvent ? '8-10' : '12-15';

		const prompt = `You are an expert event planner. Generate a focused task list for organizing a ${eventType} event.

    ${eventTitle ? `Event Title: ${eventTitle}` : ''}
    ${eventDescription ? `Event Description: ${eventDescription}` : ''}
    ${eventContext}
    ${eventSpecificTasks}

    ${
			isSubEvent
				? 'This is a sub-event that is part of a larger main event.'
				: 'This is a standalone main event.'
		}

    Create ONLY the most essential tasks that cover critical aspects:
    - Initial planning and setup
    - Logistics and coordination  
    - Execution and management
    - Post-event activities (if applicable)

    Distribute tasks across these columns:
    - "planning" (initial setup and preparation)
    - "developing" (active development and creation)
    - "reviewing" (quality checks and final preparations)
    - "finished" (completed or day-of tasks)

    Priority levels:
    - "high" for critical path items
    - "medium" for important but flexible items
    - "low" for nice-to-have items

    IMPORTANT CONSTRAINTS:
    - Generate EXACTLY ${taskCount} tasks total
    - Each task should have 2-4 relevant subtasks
    - Total subtasks across all tasks should not exceed ${subtaskCount}
    - Focus on event-specific needs based on the event type
    - Keep tasks actionable and specific
    - Estimated duration should be realistic (e.g., "2 hours", "1 day", "3 days", "1 week")

    Make tasks specific and actionable, considering the event type and details provided.`;

		// Generate tasks using AI SDK with text generation
		let tasksData: { tasks: any[] };
		try {
			const result = await generateText({
				model: google('gemini-2.0-flash-exp'),
				prompt:
					prompt +
					'\n\nReturn ONLY a valid JSON object with this structure: {"tasks": [...]}',
			});

			// Parse the JSON response
			tasksData = JSON.parse(result.text);

			// Validate the structure
			if (!tasksData.tasks || !Array.isArray(tasksData.tasks)) {
				throw new Error('Invalid response structure');
			}
		} catch (aiError) {
			console.error('AI generation failed:', aiError);
			throw new Error('Failed to generate tasks with AI');
		}

		// Transform tasks to include IDs
		const tasksWithIds = tasksData.tasks.map((task: any, index: number) => {
			const baseId = Date.now();
			return {
				id: `task_${baseId}_${index}`,
				content: task.content,
				column: task.column,
				priority: task.priority,
				estimatedDuration: task.estimatedDuration,
				subtasks: (task.subtasks || []).map(
					(subtask: any, subIndex: number) => ({
						id: `subtask_${baseId}_${index}_${subIndex}`,
						content: subtask.content,
					})
				),
			};
		});

		// Save tasks to database
		const saveResult = await saveEventTasks(
			eventId,
			mongoUser._id.toString(),
			tasksWithIds
		);
		if (!saveResult.success) {
			throw new Error(saveResult.error || 'Failed to save tasks');
		}

		return NextResponse.json({
			success: true,
			tasks: tasksWithIds,
			message: 'Tasks generated and saved successfully',
		});
	} catch (error) {
		console.error('Error generating tasks:', error);

		// Return fallback tasks in case of AI failure
		const fallbackTasks = getFallbackTasks(isSubEvent || false);

		if (mongoUser && eventId) {
			try {
				await saveEventTasks(eventId, mongoUser._id.toString(), fallbackTasks);
				return NextResponse.json({
					success: true,
					tasks: fallbackTasks,
					message: 'Fallback tasks generated due to AI service error',
					fallback: true,
				});
			} catch (saveError) {
				return NextResponse.json(
					{
						error: 'Failed to generate and save fallback tasks',
						message: error instanceof Error ? error.message : 'Unknown error',
					},
					{ status: 500 }
				);
			}
		}

		return NextResponse.json(
			{
				error: 'Failed to generate tasks',
				message: error instanceof Error ? error.message : 'Unknown error',
			},
			{ status: 500 }
		);
	}
}

// Fallback tasks function
function getFallbackTasks(isSubEvent?: boolean) {
	const baseId = Date.now();

	if (isSubEvent) {
		return [
			{
				id: `fallback_${baseId}_1`,
				content: 'Coordinate with main event team',
				column: 'planning',
				priority: 'high' as const,
				estimatedDuration: '1 hour',
				subtasks: [
					{
						id: `fallback_sub_${baseId}_1_1`,
						content: 'Review main event timeline',
					},
					{
						id: `fallback_sub_${baseId}_1_2`,
						content: 'Align sub-event objectives',
					},
					{
						id: `fallback_sub_${baseId}_1_3`,
						content: 'Confirm resource allocation',
					},
				],
			},
			{
				id: `fallback_${baseId}_2`,
				content: 'Plan sub-event logistics',
				column: 'planning',
				priority: 'high' as const,
				estimatedDuration: '2 hours',
				subtasks: [
					{
						id: `fallback_sub_${baseId}_2_1`,
						content: 'Define space requirements',
					},
					{ id: `fallback_sub_${baseId}_2_2`, content: 'Schedule setup time' },
				],
			},
			{
				id: `fallback_${baseId}_3`,
				content: 'Develop content and materials',
				column: 'developing',
				priority: 'medium' as const,
				estimatedDuration: '1 day',
				subtasks: [
					{
						id: `fallback_sub_${baseId}_3_1`,
						content: 'Create presentation materials',
					},
					{ id: `fallback_sub_${baseId}_3_2`, content: 'Prepare handouts' },
				],
			},
			{
				id: `fallback_${baseId}_4`,
				content: 'Final preparations',
				column: 'reviewing',
				priority: 'high' as const,
				estimatedDuration: '2 hours',
				subtasks: [
					{ id: `fallback_sub_${baseId}_4_1`, content: 'Test equipment' },
					{ id: `fallback_sub_${baseId}_4_2`, content: 'Brief team members' },
				],
			},
		];
	}

	return [
		{
			id: `fallback_${baseId}_1`,
			content: 'Define event concept and objectives',
			column: 'planning',
			priority: 'high' as const,
			estimatedDuration: '2 hours',
			subtasks: [
				{ id: `fallback_sub_${baseId}_1_1`, content: 'Set clear event goals' },
				{ id: `fallback_sub_${baseId}_1_2`, content: 'Define target audience' },
				{ id: `fallback_sub_${baseId}_1_3`, content: 'Create event theme' },
			],
		},
		{
			id: `fallback_${baseId}_2`,
			content: 'Secure venue and logistics',
			column: 'planning',
			priority: 'high' as const,
			estimatedDuration: '1 week',
			subtasks: [
				{
					id: `fallback_sub_${baseId}_2_1`,
					content: 'Research and book venue',
				},
				{ id: `fallback_sub_${baseId}_2_2`, content: 'Arrange catering' },
				{ id: `fallback_sub_${baseId}_2_3`, content: 'Plan transportation' },
			],
		},
		{
			id: `fallback_${baseId}_3`,
			content: 'Develop marketing strategy',
			column: 'developing',
			priority: 'medium' as const,
			estimatedDuration: '3 days',
			subtasks: [
				{
					id: `fallback_sub_${baseId}_3_1`,
					content: 'Create promotional materials',
				},
				{
					id: `fallback_sub_${baseId}_3_2`,
					content: 'Set up social media campaigns',
				},
				{
					id: `fallback_sub_${baseId}_3_3`,
					content: 'Reach out to media contacts',
				},
			],
		},
		{
			id: `fallback_${baseId}_4`,
			content: 'Coordinate speakers and content',
			column: 'developing',
			priority: 'high' as const,
			estimatedDuration: '1 week',
			subtasks: [
				{ id: `fallback_sub_${baseId}_4_1`, content: 'Confirm speaker lineup' },
				{
					id: `fallback_sub_${baseId}_4_2`,
					content: 'Prepare presentation materials',
				},
				{ id: `fallback_sub_${baseId}_4_3`, content: 'Schedule rehearsals' },
			],
		},
		{
			id: `fallback_${baseId}_5`,
			content: 'Final event preparations',
			column: 'reviewing',
			priority: 'high' as const,
			estimatedDuration: '2 days',
			subtasks: [
				{
					id: `fallback_sub_${baseId}_5_1`,
					content: 'Conduct final venue walkthrough',
				},
				{ id: `fallback_sub_${baseId}_5_2`, content: 'Brief all team members' },
				{ id: `fallback_sub_${baseId}_5_3`, content: 'Test all equipment' },
			],
		},
		{
			id: `fallback_${baseId}_6`,
			content: 'Execute event day',
			column: 'finished',
			priority: 'high' as const,
			estimatedDuration: '1 day',
			subtasks: [
				{ id: `fallback_sub_${baseId}_6_1`, content: 'Set up venue' },
				{ id: `fallback_sub_${baseId}_6_2`, content: 'Manage event flow' },
				{ id: `fallback_sub_${baseId}_6_3`, content: 'Handle any issues' },
			],
		},
	];
}
